BEGIN;

CREATE TABLE IF NOT EXISTS ins_tyre_alert_configs (
    id VARCHAR(40) PRIMARY KEY,
    parent_asset_id VARCHAR(40) UNIQUE NOT NULL,

    use_pressure_alert BOOLEAN NOT NULL DEFAULT FALSE,
    use_high_temperature_alert BOOLEAN NOT NULL DEFAULT FALSE,
    max_temperature_threshold DOUBLE PRECISION,

    -- use_tyre_pressure_mismatch_alert BOOLEAN NOT NULL DEFAULT FALSE,
    -- use_tyre_data_loss_alert BOOLEAN NOT NULL DEFAULT FALSE,

    use_send_notification BOOLEAN NOT NULL DEFAULT FALSE,
    notification_action_types VARCHAR(255)[],
    notification_recipient_user_ids VARCHAR(40)[],
    notify_asset_assigne BOOLEAN NOT NULL DEFAULT FALSE,

    use_create_tickets BOOLEAN NOT NULL DEFAULT FALSE,
    ticket_assigned_user_id VARCHAR(40),

    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at TIMESTAMPTZ,
    updated_by VA<PERSON>HAR(40) NOT NULL,
    created_by <PERSON><PERSON><PERSON><PERSON>(40) NOT NULL,
    client_id VARCHAR(40) NOT NULL
);


CREATE TABLE IF NOT EXISTS "ins_TYRE_ALERT_TYPE" (
    code VARCHAR(255) PRIMARY KEY,
    label VARCHAR(255) NOT NULL,
    description TEXT NOT NULL
);

INSERT INTO
    "ins_TYRE_ALERT_TYPE" (code, "label", description)
VALUES
    ('PRESSURE_OVERINFLATED', 'Pressure Overinflated', '-'),
    ('PRESSURE_UNDERINFLATED', 'Pressure Underinflated', '-'),
    ('HIGH_TEMPERATURE', 'High Temperature', '-')
    ON CONFLICT (code) DO NOTHING;


CREATE TABLE IF NOT EXISTS ins_tyre_alerts (
    id VARCHAR(40) PRIMARY KEY,
    tyre_alert_config_id VARCHAR(40) REFERENCES "ins_tyre_alert_configs"(id),
    asset_id VARCHAR(40) NOT NULL,
    parent_asset_id VARCHAR(40) NOT NULL,
    "time" TIMESTAMPTZ,
    tyre_alert_type_code VARCHAR(255) REFERENCES "ins_TYRE_ALERT_TYPE"(code),
    recorded_value JSONB DEFAULT '{}'::jsonb,
    threshold_value JSONB DEFAULT '{}'::jsonb,
    is_read BOOLEAN DEFAULT false NOT NULL, 
    read_datetime timestamptz NULL,
    ticket_id VARCHAR(40),

    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at TIMESTAMPTZ,
    updated_by VARCHAR(40) NOT NULL,
    created_by VARCHAR(40) NOT NULL,
    client_id VARCHAR(40) NOT NULL
);

COMMIT;